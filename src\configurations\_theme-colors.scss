$primary: (
	base: #4f8406,
	lighten: #d5e7bb,
	darken: #395f05,
);

$secondary: (
	base: #e2c076,
	lighten: #f1e6cc,
	darken: #968254,
);

$secondary-green: (
	base: #3e4e4b,
	lighten: #6e7a78,
	darken: #00231d,
);

$secondary-golden: (
	base: #e2c076,
	lighten: #f1e6cc,
	darken: #968254,
);

$neutral: (
	base: #e0e0e0,
	lighten: #f5f5f5,
	darken: #5c5c5c,
);

$error: (
	base: #f44336,
	lighten: #ffebee,
	darken: #b71c1c,
);

$warning: (
	base: #ff9800,
	lighten: #fff3e0,
	darken: #ff6d00,
);

$info: (
	base: #2196f3,
	lighten: #e3f2fd,
	darken: #0d47a1,
);

$success: (
	base: #4caf50,
	lighten: #e8f5e9,
	darken: #1b5e20,
);

$extra-1: (
	base: #8e24aa,
	lighten: #f3e5f5,
	darken: #4a148c,
);

$extra-2: (
	base: #ec407a,
	lighten: #fce4ec,
	darken: #880e4f,
);

$gray: (
	base: #858585,
	lighten: #d6d6d6,
	darken: #5c5c5c,
);

$theme-colors: (
	'primary': $primary,
	'secondary': $secondary,
	'secondary-green': $secondary-green,
	'secondary-golden': $secondary-golden,
	'neutral': $neutral,
	'info': $info,
	'error': $error,
	'warning': $warning,
	'success': $success,
	'extra-1': $extra-1,
	'extra-2': $extra-2,
	'gray': $gray,
);

$text-colors: (
	'primary': #1c1c1c,
	'secondary': #757575,
	'disabled': #858585,
);

$stroke-colors: (
	'base': #e0e0e0,
	'disabled': #bdbdbd,
	'divider': #e0e0e0,
);

$bw-colors: (
	'black': #1c1c1c,
	'black-80': #333333,
	'black-50': #5c5c5c,
	'black-40': #757575,
	'black-30': #858585,
	'black-10': #d6d6d6,
	'black-5': #f5f5f5,
	'white': #ffffff,
);

$background-colors: (
	'base': #f5f5f5,
	'lighten': #FAFAFA,
	'white': #FFFFFF,
);

$theme-colors-list: map-keys($theme-colors);

@function themeColor($name, $variation: 'base') {
	$theme-color: map-get($theme-colors, $name);
	@return map-get($theme-color, $variation);
}