@import '../../configurations/theme-colors';



.farm-switch {
    @each $color in $theme-colors-list {
        &#{'[color=' + $color + ']'} {
            .farm-switch--selected {
                background-color: themeColor($color);
            }

            .farm-switch--disabled-on {
                background-color: rgba(themeColor($color), 0.27);
            }
        }
    }
}

.farm-switch--idle {
    background-color: var(--farm-neutral-base);
}

.farm-switch--disabled-off {
    background-color: var(--farm-neutral-lighten);
}

.farm-switch {
    display: inline-block;
    position: relative;

    width: 24px;
    height: 16px;
    border-radius: 9999px;

    &:focus {
        outline: 0;
    }

    &[block] {
        display: block;
    }

    cursor: pointer;

    &:has(.farm-switch--disabled-on),
    &:has(.farm-switch--disabled-off) {
        cursor: default;
    }

}

.farm-switch__background {
    display: block;
    border-radius: 9999px;
    margin-top: 0;
    height: 12px;
    width: 100%;
    transition: background-color .4s ease;
}

.farm-switch__indicator {
    position: absolute;
    height: 16px;
    width: 16px;
    left: -4px;
    top: -2px;
    background-color: #ffffff;
    border-radius: 9999px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.16);
    transition: transform .4s ease;
}