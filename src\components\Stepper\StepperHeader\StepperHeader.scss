$step-height: 64px;

@import 'mixins.scss';

.horizontal-step-size .stepper__header--vertical {
	width: 160px;
}

.stepper__header {
	display: flex;
	flex-direction: row;
	justify-content: space-between;

	div.stepper__header-step {
		width: 80px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-items: center;
		color: var(--farm-bw-black-5);
		font-size: 14px;
		font-weight: 700;

		&.stepper__header-step--previous {
			@include stepperHeaderStepColor(var(--farm-primary-base));
		}

		&.stepper__header-step--current {
			@include stepperHeaderStepColor(var(--farm-info-base));
		}

		&.stepper__header-step--error {
			@include stepperHeaderStepColor(var(--farm-error-base));
		}

		&.stepper__header-step--next {
			.farm-icon,
			.farm-icon__number {
				color: var(--farm-bw-black-50);
			}
		}

		.farm-bodytext--2:not(.farm-icon__number) {
			margin-top: 8px;
			text-align: center;
		}
	}

	.stepper__divider--horizontal {
		display: block;
		flex: 1 1 0px;
		max-width: 100%;
		height: 1px;

		border: none;
		margin: 16px -16px 0;
		background: var(--farm-bw-black-5);

		&.stepper__divider--previous {
			background: var(--farm-primary-base);
		}

		@include stepperDividerGradient('right');
	}

	&.stepper__header--vertical {
		display: flex;
		flex-direction: column;

		div.stepper__header-step {
			width: 100%;
			display: block;
			clear: both;
			height: 64px;

			.farm-icon {
				float: left;
			}

			.farm-bodytext--2 {
				display: flex;
				align-items: center;
				width: calc(100% - 40px);
				height: 32px;
				clear: none;
				float: left;
				margin-top: 0;
				margin-left: 8px;
				text-align: left;
			}
		}

		.stepper__divider--vertical {
			float: left;
			clear: both;
			height: 32px;
			margin: -24px 0 8px 16px;
			width: 1px;
			background-color: var(--farm-bw-black-5);

			&.stepper__divider--previous {
				background-color: var(--farm-primary-base);
			}

			@include stepperDividerGradient('bottom');
		}
	}
}

.farm-icon,
.farm-icon__number {
	border: 1px solid var(--farm-bw-black-5);
	border-radius: 50%;
	width: 32px;
	height: 32px;

	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-style: normal;

	&:before {
		font-size: 16px;
		color: var(--farm-bw-black-50);
	}
}
