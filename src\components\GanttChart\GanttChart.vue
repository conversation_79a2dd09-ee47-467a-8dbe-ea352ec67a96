<template>
	<div class="farm-gantt-chart" :style="componentStyle">
		<div class="farm-gantt-chart__header">
			<div class="farm-gantt-chart__row-label-space"/>
			<div class="farm-gantt-chart__timeline" :style="timelineGridStyle">
				<div
					v-for="(month, index) in monthColumns"
					:key="index"
					class="farm-gantt-chart__month-header"
					:class="{ 'farm-gantt-chart__month-header--current': month.isCurrentMonth }"
				>
					<farm-typography
						size="md"
						:weight="500"
						:color="month.isCurrentMonth ? 'primary' : 'black'"
						:color-variation="month.isCurrentMonth ? '' : '50'"
						class="mb-0"
					>
						{{ month.label }}
					</farm-typography>
				</div>
			</div>
		</div>

		<!-- Gantt Chart Content -->
		<div class="farm-gantt-chart__content">
			<div
				v-for="(group, groupIndex) in data.groups"
				:key="'group-' + groupIndex"
				class="farm-gantt-chart__group"
			>
				<!-- Group label -->
				<div class="farm-gantt-chart__group-label">
					<farm-typography :weight="500">
						{{ group.title }}
					</farm-typography>
				</div>

				<!-- Group timeline with grid and bars -->
				<div class="farm-gantt-chart__group-timeline" :style="timelineGridStyle">
					<!-- Bars positioned using CSS Grid -->
					<div
						v-for="(bar, barIndex) in getPositionedBars(group.bars)"
						:key="'bar-' + barIndex"
						class="farm-gantt-chart__bar"
						:style="getBarGridStyle(bar)"
						@click="$emit('bar-click', bar)"
					>
						<farm-typography size="md" :weight="500" color="white" class="mb-0" ellipsis>
							{{ bar.label }}
						</farm-typography>
					</div>
				</div>
			</div>
		</div>

		<!-- Legend -->
		<div class="farm-gantt-chart__legend" v-if="autoGeneratedLegend.length > 0">
			<div class="farm-gantt-chart__legend-title">
				<farm-typography size="md" :weight="700" color="black" color-variation="50">
					Legenda:
				</farm-typography>
			</div>
			<div
				v-for="(item, index) in autoGeneratedLegend"
				:key="'legend-' + index"
				class="farm-gantt-chart__legend-item"
			>
				<div
					class="farm-gantt-chart__legend-color"
					:style="{ backgroundColor: item.color }"
				></div>
				<div class="farm-gantt-chart__legend-label">
					<farm-typography size="md" color="black" color-variation="50">
						{{ item.label }}
					</farm-typography>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType, computed } from 'vue';
import type { GanttData, GanttBar } from './types';
import {
	getMonthsBetween,
	formatMonth,
	isCurrentMonth,
	getDaysInMonth,
	getColumnForDate,
	parseDate
} from './utils/dateUtils';

export default defineComponent({
	name: 'farm-gantt-chart',
	props: {
	
		data: {
			type: Object as PropType<GanttData>,
			required: true,
		},
	},
	emits: ['bar-click'],
	setup(props) {
		// Cálculo automático de datas baseado nas barras
		// Compatível com strings ISO (YYYY-MM-DD) e Date objects
		const autoCalculatedDateRange = computed(() => {
			const allDates: Date[] = [];

			props.data.groups.forEach(group => {
				group.bars.forEach(bar => {
					const startDate = parseDate(bar.start);
					const endDate = parseDate(bar.end);

					if (!isNaN(startDate.getTime())) {
						allDates.push(startDate);
					}
					if (!isNaN(endDate.getTime())) {
						allDates.push(endDate);
					}
				});
			});

			if (allDates.length === 0) {
				// Fallback para caso não haja barras válidas
				const now = new Date();
				return {
					start: new Date(now.getFullYear(), 0, 1),
					end: new Date(now.getFullYear(), 11, 31)
				};
			}

			const minDate = new Date(Math.min(...allDates.map(d => d.getTime())));
			const maxDate = new Date(Math.max(...allDates.map(d => d.getTime())));

			// Ajustar para início/fim do mês conforme o plano
			minDate.setDate(1); // primeiro dia do mês
			maxDate.setMonth(maxDate.getMonth() + 1, 0); // último dia do mês

			return { start: minDate, end: maxDate };
		});

		// Geração automática da legenda baseada nas cores e labels únicos
		const autoGeneratedLegend = computed(() => {
			const uniqueItems = new Map<string, { label: string; color: string }>();

			props.data.groups.forEach(group => {
				group.bars.forEach(bar => {
					const key = `${bar.color}-${bar.label}`;
					if (!uniqueItems.has(key)) {
						uniqueItems.set(key, {
							label: bar.label,
							color: bar.color
						});
					}
				});
			});

			return Array.from(uniqueItems.values());
		});

		// Generate month columns using calculated date range
		const monthColumns = computed(() => {
			const { start, end } = autoCalculatedDateRange.value;
			const months = getMonthsBetween(start, end);
			return months.map(month => ({
				date: month,
				label: formatMonth(month),
				isCurrentMonth: isCurrentMonth(month),
			}));
		});

		// CSS Grid template for timeline
		const timelineGridStyle = computed(() => ({
			gridTemplateColumns: `repeat(${monthColumns.value.length}, 80px)`,
		}));

		// Get today's column position
		const todayColumn = computed(() => {
			const today = new Date();
			const { start } = autoCalculatedDateRange.value;
			const column = getColumnForDate(today, start);
			// Only show if today is within the chart range
			return column >= 0 && column < monthColumns.value.length ? column : -1;
		});

		// Get CSS Grid style for a bar
		// Compatível com strings ISO e Date objects
		const getBarGridStyle = (bar: GanttBar) => {
			const barStartDate = parseDate(bar.start);
			let barEndDate = parseDate(bar.end);
			const { start: chartStartDate } = autoCalculatedDateRange.value;

			// Validate dates
			if (isNaN(barStartDate.getTime()) || isNaN(barEndDate.getTime())) {
				return {
					gridColumn: '1 / 2',
					backgroundColor: bar.color,
					gridRow: `${(bar.rowPosition || 0) + 1}`,
				};
			}

			if (barEndDate < barStartDate) {
				barEndDate = new Date(barStartDate.getTime()); // Ensure end is not before start
			}

			const startMonth = barStartDate.getMonth();
			const startYear = barStartDate.getFullYear();
			const startDay = barStartDate.getDate();

			const endMonth = barEndDate.getMonth();
			const endYear = barEndDate.getFullYear();
			const endDay = barEndDate.getDate();

			const daysInStartMonth = getDaysInMonth(startYear, startMonth);
			const daysInEndMonth = getDaysInMonth(endYear, endMonth);

			const startColumnIndex = getColumnForDate(barStartDate, chartStartDate);
			const endColumnIndex = getColumnForDate(barEndDate, chartStartDate);

			const gridColumnStartValue = Math.max(1, startColumnIndex + 1);
			const gridColumnEndValue = Math.min(monthColumns.value.length + 1, endColumnIndex + 2);

			const numCssGridColumnsSpanned = gridColumnEndValue - gridColumnStartValue;

			let marginLeftStyle = '0%';
			let widthStyle = '100%';

			// Calculate the actual number of visual months the bar spans
			const visualStartCol = getColumnForDate(barStartDate, chartStartDate);
			const visualEndCol = getColumnForDate(barEndDate, chartStartDate);
			const numVisualMonthsSpanned = visualEndCol - visualStartCol + 1;

			if (numVisualMonthsSpanned === 1) {
				// Bar is within a single month column visually
				const barStartFractionInMonth = (startDay - 1) / daysInStartMonth;
				const effectiveEndDay = (startYear === endYear && startMonth === endMonth) ? endDay : daysInStartMonth;
				const barEndFractionInMonth = effectiveEndDay / daysInStartMonth;

				marginLeftStyle = `calc(${barStartFractionInMonth * 100}%)`;
				widthStyle = `calc(${(barEndFractionInMonth - barStartFractionInMonth) * 100}%)`;

			} else if (numVisualMonthsSpanned > 1) {
				// Bar spans multiple month columns visually
				const fractionBeforeBarInStartMonth = (startDay - 1) / daysInStartMonth;
				const fractionOfBarInStartMonth = (daysInStartMonth - (startDay - 1)) / daysInStartMonth;
				const fractionOfBarInEndMonth = endDay / daysInEndMonth;

				const numFullIntermediateMonths = Math.max(0, numVisualMonthsSpanned - 2);

				if (numCssGridColumnsSpanned > 0) {
					marginLeftStyle = `calc((${fractionBeforeBarInStartMonth} / ${numCssGridColumnsSpanned}) * 100%)`;

					const totalEffectiveVisualColumnsOccupiedByBar = fractionOfBarInStartMonth + numFullIntermediateMonths + fractionOfBarInEndMonth;
					widthStyle = `calc((${totalEffectiveVisualColumnsOccupiedByBar} / ${numCssGridColumnsSpanned}) * 100%)`;
				} else {
					marginLeftStyle = '0%';
					widthStyle = '100%';
				}
			}

			return {
				'grid-column-start': gridColumnStartValue,
				'grid-column-end': gridColumnEndValue,
				'background-color': bar.color,
				'grid-row': `${(bar.rowPosition || 0) + 1}`,
				'margin-left': marginLeftStyle,
				'width': widthStyle,
			};
		};

		// Calculate vertical positions for bars to avoid overlapping
		const getPositionedBars = (bars: GanttBar[]) => {
			if (!bars || bars.length === 0) return [];

			// Clone the bars to avoid modifying the original
			const positionedBars = JSON.parse(JSON.stringify(bars));

			// Sort bars by start date
			positionedBars.sort((a: GanttBar, b: GanttBar) => {
				return new Date(a.start).getTime() - new Date(b.start).getTime();
			});

			// Track occupied rows
			const occupiedUntil: number[] = [];

			// Assign row positions
			positionedBars.forEach((bar: GanttBar) => {
				// Ensure we have valid dates usando parseDate para compatibilidade
				const startDate = parseDate(bar.start);
				const endDate = parseDate(bar.end);

				// Validate dates and use fallback if invalid
				const barStart = isNaN(startDate.getTime()) ? Date.now() : startDate.getTime();
				const barEnd = isNaN(endDate.getTime()) ? Date.now() + 86400000 : endDate.getTime(); // +1 day fallback

				// Find the first available row
				let rowPosition = 0;
				while (occupiedUntil[rowPosition] && occupiedUntil[rowPosition] > barStart) {
					rowPosition++;
				}

				// Assign the row position to the bar
				bar.rowPosition = rowPosition;

				// Mark this row as occupied until the bar ends
				occupiedUntil[rowPosition] = barEnd;
			});

			return positionedBars;
		};

		// Calculate content height based on groups
		const contentHeight = computed(() => {
			let totalHeight = 0;
			props.data.groups.forEach(group => {
				const positionedBars = getPositionedBars(group.bars);
				const maxRows = Math.max(
					1,
					...positionedBars.map(bar => (bar.rowPosition || 0) + 1)
				);
				const groupHeight = Math.max(60, maxRows * 35 + 10); // 35px per row + padding
				totalHeight += groupHeight + 20; // 20px margin between groups
			});
			return totalHeight;
		});

		// CSS variables for the component
		const componentStyle = computed(() => ({
			'--gantt-content-height': `${contentHeight.value}px`,
		}));

		return {
			monthColumns,
			timelineGridStyle,
			todayColumn,
			autoGeneratedLegend,
			getBarGridStyle,
			getPositionedBars,
			componentStyle,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './GanttChart';
</style>
