import { Meta } from '@storybook/addon-docs';

<Meta title="Layout/Props Values" />

# About
Many components have props that accepts strings as values instead of passa a numeric values.
Example:
<code>
&#x3C;my-component some-prop=&#x22;md&#x22; /&#x3E;<br />
//"md" will be used as 12px in the CSS
</code>
<br /><br />

## Gutter
* none: 0
* xs: 4px
* sm: 8px
* vuetify: 12px
* md: 16px
* lg: 24px
* xl: 32px
* default: 24px

## Font Size
* xs: 8px
* sm: 12px
* md: 14px
* default: 16px
* lg: 20px
* xl: 24px
