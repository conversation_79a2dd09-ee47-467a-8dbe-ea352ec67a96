import { withDesign } from 'storybook-addon-designs';
import GanttChart from './GanttChart.vue';

export default {
  title: 'Display/GanttChart',
  component: GanttChart,
  decorators: [withDesign],
  parameters: {
    docs: {
      description: {
        component: `Gantt Chart - Componente com API simplificada que calcula automaticamente datas e legenda<br />
        selector: <em>farm-gantt-chart</em><br />
        <span style="color: var(--farm-primary-base);">ready for use</span>
        `,
      },
    },
    viewMode: 'docs',
  },
};

export const Primary = () => ({
  data() {
    return {
      // Seguindo padrão da biblioteca - formato ISO YYYY-MM-DD
      ganttData: {
        groups: [
          {
            title: 'Campanha Safrinha 25',
            bars: [
              {
                id: 1,
                label: 'Vigência da Campanha',
                start: '2025-01-01', // Formato ISO padrão da biblioteca
                end: '2025-06-15',
                color: '#7BC4F7', // Azul - cor direta
              },
              {
                id: 2,
                label: 'Vigência do Produto Comercial',
                start: '2025-01-15',
                end: '2025-05-15',
                color: '#8BB455', // Verde - cor direta
              },
              {
                id: 3,
                label: 'Período de Desembolso',
                start: '2025-03-01',
                end: '2025-05-30',
                color: '#FFB84D', // Laranja - cor direta
              },
              {
                id: 4,
                label: 'Intervalo Vencimento',
                start: '2025-04-01',
                end: '2025-05-15',
                color: '#F7857F', // Vermelho - cor direta
              },
            ],
          },
          {
            title: 'Campanha Safra 25',
            bars: [
              {
                id: 5,
                label: 'Vigência da Campanha',
                start: '2025-05-01',
                end: '2025-12-31',
                color: '#7BC4F7', // Azul - cor direta
              },
              {
                id: 6,
                label: 'Vigência do Produto Comercial',
                start: '2025-05-15',
                end: '2025-11-15',
                color: '#8BB455', // Verde - cor direta
              },
              {
                id: 7,
                label: 'Período de Desembolso',
                start: '2025-06-01',
                end: '2025-08-30',
                color: '#FFB84D', // Laranja - cor direta
              },
              {
                id: 8,
                label: 'Intervalo Vencimento',
                start: '2025-07-01',
                end: '2025-12-15',
                color: '#F7857F', // Vermelho - cor direta
              },
            ],
          },
        ],
      },
    };
  },
  methods: {
    handleBarClick(bar) {
      alert(`Clicou em: ${bar.label}`);
    }
  },
  template: `<div style="width: 100%; height: 600px; padding: 20px;">
    <farm-gantt-chart
      :data="ganttData"
      @bar-click="handleBarClick"
    />
  </div>`,
});

export const CustomColors = () => ({
  data() {
    return {
      ganttData: {
        groups: [
          {
            title: 'Projeto A',
            bars: [
              {
                id: 1,
                label: 'Fase 1',
                start: '2025-01-15', // Formato ISO padrão
                end: '2025-03-15',
                color: '#8E44AD', // Roxo personalizado
              },
              {
                id: 2,
                label: 'Fase 2',
                start: '2025-03-01',
                end: '2025-05-01',
                color: '#16A085', // Verde personalizado
              },
            ],
          },
          {
            title: 'Projeto B',
            bars: [
              {
                id: 3,
                label: 'Fase 1',
                start: '2025-02-01',
                end: '2025-04-01',
                color: '#D35400', // Laranja personalizado
              },
              {
                id: 4,
                label: 'Fase 2',
                start: '2025-04-15',
                end: '2025-06-15',
                color: '#2980B9', // Azul personalizado
              },
            ],
          },
        ],
      },
    };
  },
  template: `<div style="width: 100%; height: 300px; padding: 20px;">
    <farm-gantt-chart :data="ganttData" />
  </div>`,
});

export const MinimalSetup = () => ({
  data() {
    return {
      ganttData: {
        groups: [
          {
            title: 'Projeto X',
            bars: [
              {
                id: 1,
                label: 'Planejamento',
                start: '2025-01-01', // Formato ISO padrão
                end: '2025-02-28',
                color: '#7BC4F7', // Azul
              },
              {
                id: 2,
                label: 'Execução',
                start: '2025-03-01',
                end: '2025-09-30',
                color: '#8BB455', // Verde
              },
              {
                id: 3,
                label: 'Finalização',
                start: '2025-10-01',
                end: '2025-12-31',
                color: '#F7857F', // Vermelho
              },
            ],
          },
        ],
      },
    };
  },
  template: `<div style="width: 100%; height: 200px; padding: 20px;">
    <farm-gantt-chart :data="ganttData" />
  </div>`,
});

export const CurrentTimelineExample = () => ({
  data() {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // getMonth() retorna 0-11, precisamos 1-12

    // Função helper para formatar data no padrão ISO
    const formatDate = (year, month, day) => {
      const monthStr = month.toString().padStart(2, '0');
      const dayStr = day.toString().padStart(2, '0');
      return `${year}-${monthStr}-${dayStr}`;
    };

    return {
      ganttData: {
        groups: [
          {
            title: 'Projeto Atual',
            bars: [
              {
                id: 1,
                label: 'Fase Inicial',
                start: formatDate(currentYear, Math.max(1, currentMonth - 1), 5),
                end: formatDate(currentYear, currentMonth, 10),
                color: '#7BC4F7', // Azul
              },
              {
                id: 2,
                label: 'Fase Intermediária',
                start: formatDate(currentYear, currentMonth, 5),
                end: formatDate(currentYear, Math.min(12, currentMonth + 1), 15),
                color: '#8BB455', // Verde
              },
              {
                id: 3,
                label: 'Fase Final',
                start: formatDate(currentYear, Math.min(12, currentMonth + 1), 10),
                end: formatDate(currentYear, Math.min(12, currentMonth + 1), 28),
                color: '#F7857F', // Vermelho
              },
            ],
          },
        ],
      },
    };
  },
  template: `<div style="width: 100%; height: 200px; padding: 20px;">
    <farm-gantt-chart :data="ganttData" />
  </div>`,
});

export const LibraryThemeColors = () => ({
  data() {
    return {
      ganttData: {
        groups: [
          {
            title: 'Demonstração das Cores da Biblioteca',
            bars: [
              {
                id: 1,
                label: 'Info (Azul)',
                start: '2025-01-01', // Formato ISO padrão
                end: '2025-03-31',
                color: '#7BC4F7', // Info
              },
              {
                id: 2,
                label: 'Primary (Verde)',
                start: '2025-04-01',
                end: '2025-06-30',
                color: '#8BB455', // Primary
              },
              {
                id: 3,
                label: 'Warning (Laranja)',
                start: '2025-07-01',
                end: '2025-09-30',
                color: '#FFB84D', // Warning
              },
              {
                id: 4,
                label: 'Error (Vermelho)',
                start: '2025-10-01',
                end: '2025-12-31',
                color: '#F7857F', // Error
              },
            ],
          },
          {
            title: 'Cores Extras da Biblioteca',
            bars: [
              {
                id: 5,
                label: 'Success (Verde Claro)',
                start: '2025-02-01',
                end: '2025-04-30',
                color: '#81C784', // Success
              },
              {
                id: 6,
                label: 'Secondary (Dourado)',
                start: '2025-05-01',
                end: '2025-07-31',
                color: '#EDD5A3', // Secondary
              },
              {
                id: 7,
                label: 'Extra-1 (Roxo)',
                start: '2025-08-01',
                end: '2025-10-31',
                color: '#B968C7', // Extra-1
              },
              {
                id: 8,
                label: 'Extra-2 (Rosa)',
                start: '2025-11-01',
                end: '2025-12-31',
                color: '#F2849F', // Extra-2
              },
            ],
          },
        ],
      },
    };
  },
  template: `<div style="width: 100%; height: 400px; padding: 20px;">
    <farm-gantt-chart :data="ganttData" />
  </div>`,
});

export const AutomaticFeatures = () => ({
  data() {
    return {
      ganttData: {
        groups: [
          {
            title: 'Projeto com Cálculos Automáticos',
            bars: [
              {
                id: 1,
                label: 'Design',
                start: '2025-01-01', // Formato ISO padrão
                end: '2025-02-15',
                color: '#8E44AD', // Roxo
              },
              {
                id: 2,
                label: 'Desenvolvimento Frontend',
                start: '2025-02-01',
                end: '2025-04-30',
                color: '#16A085', // Verde escuro
              },
              {
                id: 3,
                label: 'Desenvolvimento Backend',
                start: '2025-03-15',
                end: '2025-05-31',
                color: '#D35400', // Laranja
              },
              {
                id: 4,
                label: 'Testes',
                start: '2025-05-15',
                end: '2025-06-30',
                color: '#2980B9', // Azul
              },
              {
                id: 5,
                label: 'Deploy',
                start: '2025-07-01',
                end: '2025-07-15',
                color: '#E74C3C', // Vermelho
              },
            ],
          },
        ],
      },
    };
  },
  template: `<div style="width: 100%; height: 300px; padding: 20px;">
    <h3>API Simplificada - Demonstração</h3>
    <p>✅ <strong>Datas calculadas automaticamente:</strong> Janeiro 2025 até Junho 2025 (baseado nas barras)</p>
    <p>✅ <strong>Legenda gerada automaticamente:</strong> baseada nas cores e labels únicos</p>
    <p>✅ <strong>Zero configuração manual:</strong> apenas dados essenciais</p>
    <farm-gantt-chart :data="ganttData" />
  </div>`,
});