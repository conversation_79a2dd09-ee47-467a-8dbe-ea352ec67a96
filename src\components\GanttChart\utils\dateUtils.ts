/**
 * Date utility functions for GanttChart component
 * Seguindo padrões da biblioteca para compatibilidade com DatePicker/RangeDatePicker
 */

/**
 * Converte string ISO (YYYY-MM-DD) ou Date object para Date object
 * Compatível com o padrão da biblioteca
 */
export const parseDate = (date: Date | string): Date => {
	if (date instanceof Date) {
		return date;
	}

	// Se for string no formato ISO (YYYY-MM-DD), converte para Date
	if (typeof date === 'string') {
		// Adiciona horário para evitar problemas de timezone
		return new Date(`${date}T00:00:00`);
	}

	return new Date(); // fallback
};

/**
 * Get all months between two dates
 */
export const getMonthsBetween = (startDate: Date, endDate: Date): Date[] => {
	const months = [];
	const current = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
	const end = new Date(endDate.getFullYear(), endDate.getMonth(), 1);

	while (current <= end) {
		months.push(new Date(current));
		current.setMonth(current.getMonth() + 1);
	}

	return months;
};

/**
 * Format month in Brazilian Portuguese
 */
export const formatMonth = (date: Date): string => {
	const monthNames = [
		'Jan',
		'Fev',
		'Mar',
		'Abr',
		'Mai',
		'Jun',
		'Jul',
		'Ago',
		'Set',
		'Out',
		'Nov',
		'Dez',
	];
	return `${monthNames[date.getMonth()]}/${date.getFullYear()}`;
};

/**
 * Check if date is current month
 */
export const isCurrentMonth = (date: Date): boolean => {
	const now = new Date();
	return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
};

/**
 * Get number of days in a month
 */
export const getDaysInMonth = (year: number, month: number): number => {
	return new Date(year, month + 1, 0).getDate();
};

/**
 * Get column index for a date relative to start date
 * Compatível com strings ISO e Date objects
 */
export const getColumnForDate = (date: Date | string, startDate: Date): number => {
	// Usa parseDate para garantir compatibilidade com strings ISO
	const targetDate = parseDate(date);
	const startDateObj = parseDate(startDate);

	// Validate dates
	if (isNaN(targetDate.getTime()) || isNaN(startDateObj.getTime())) {
		return 0;
	}

	const targetMonth = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
	const startMonth = new Date(startDateObj.getFullYear(), startDateObj.getMonth(), 1);

	const yearDiff = targetMonth.getFullYear() - startMonth.getFullYear();
	const monthDiff = targetMonth.getMonth() - startMonth.getMonth();

	return yearDiff * 12 + monthDiff;
};
