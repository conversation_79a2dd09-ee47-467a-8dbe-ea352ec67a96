@import '../../configurations/mixins';

.farm-textfield {
	height: 64px;

	&--ellipsed {
		input {
			text-overflow: ellipsis;
		}
	}

	&--hiddendetails {
		height: 40px;
	}

	&--input {
		display: flex;
		align-items: center;
		gap: 8px;
		border: 1px solid;
		border-color: var(--farm-bw-black-10);
		height: 36px;
		border-radius: 5px;
		padding: 8px;
		margin-bottom: 4px;
		background-color: white;

		> button {
			display: flex;
		}

		& > input {
			flex: 1;
			outline: none;
			color: var(--farm-bw-black-50);
			font-size: 12px;
			font-weight: 400;
			max-width: 100%;
		}

		&--iconed > input {
			max-width: calc(100% - 32px);
		}

		width: 100%;
	}

	&--focused .farm-textfield--input {
		border-color: var(--farm-bw-black-30);
	}

	&--disabled {
		.farm-textfield--input {
			background-color: var(--farm-neutral-lighten);
			border-color: var(--farm-gray-lighten);
			color: var(--farm-bw-black-30);
		}

		input {
			background-color: var(--farm-neutral-lighten);
			border-color: var(--farm-gray-lighten);
			color: var(--farm-bw-black-30);
		}

		.farm-icon {
			color: var(--farm-bw-black-30);
			cursor: default;
		}
	}

	.farm-caption {
		line-height: 12px;
	}

	&__hint-text {
		@include hintText;
	}
}

.farm-textfield--touched.farm-textfield--blured.farm-textfield--validatable {
	&.farm-textfield--error {
		.farm-textfield {
			&--input {
				border-color: var(--farm-error-base);

				& > input {
					color: var(--farm-neutral-darken);
				}

				.farm-icon {
					color: var(--farm-error-base);
				}
			}
		}
	}
}

.farm-textfield--blured.farm-textfield--validatable:not(.farm-textfield--error) {
	.farm-textfield--input {
		border-color: var(--farm-primary-base);

		& > input {
			color: var(--farm-neutral-darken);
		}

		.farm-icon {
			color: var(--farm-primary-base);
		}
	}
}

.farm-textfield--uppercase input {
	text-transform: uppercase;
}
