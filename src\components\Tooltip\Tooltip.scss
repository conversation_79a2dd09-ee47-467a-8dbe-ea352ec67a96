@import '../../configurations/theme-colors';
@import '../../configurations/variables';

.farm-tooltip {
	display: inline-block;
	position: relative;

	&__activator {
		display: inline-block;
	}
}

.farm-tooltip__popup {
	background-color: rgba(themeColor('primary'), 0.95);
	@each $color in $theme-colors-list {
		&.farm-tooltip--#{$color} {
			background-color: rgba(themeColor($color), 0.95);
		}
	}
}

.farm-tooltip__popup {
	visibility: hidden;
	opacity: 0;
	transition: visibility 0.3s linear, opacity 0.3s linear;
	position: absolute;
	width: 160px;
	contain: content;
	color: white;
	border-radius: 5px;
	font-family: 'Manrope', sans-serif !important;
	font-size: 12px;
	font-weight: 500px;
	padding: 8px 12px;
	display: block;

	&--visible {
		opacity: 1;
		visibility: visible;
	}
}
