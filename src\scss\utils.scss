.container-main.container-main-sm {
	max-width: 960px;
}

tr.v-data-table__empty-wrapper {
	td {
		background-color: white !important;
		cursor: default !important;
		opacity: 1 !important;
		padding: 0 !important;
	}
}

.mf-Containers.mf-Containers-authenticated {
	> .mf-Container > div.v-application {
		padding-bottom: 2.625rem;
	}
}

#blip-chat-container {
	bottom: 1rem;

	#blip-chat-open-iframe {
		bottom: 0.5rem;
	}
}

body.body--has-footer {
	#blip-chat-container #blip-chat-open-iframe {
		bottom: 3rem;
	}
}

//temporary solution for v-data-table checkboxes
.v-data-table__checkbox.v-simple-checkbox {
	.v-icon.v-icon {
		font-size: 22px;
		color: var(--farm-neutral-farken);
		&.mdi-checkbox-marked,
		&.mdi-minus-box {
			color: var(--farm-primary-base);
		}
	}

	.v-input--selection-controls__ripple {
		display: none;
	}
}

.collapsible-stories-class-with-line {
	position: relative;
	padding-right: 20px;
	&:after {
		content: '';
		position: absolute;
		height: 80%;
		width: 1px;
		border-right: 1px solid var(--farm-stroke-base);
		right: 10px;
		top: 5%;
	}
}
