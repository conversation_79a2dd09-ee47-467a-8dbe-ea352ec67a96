# GanttChart Component

Componente de Gráfico de Gantt com API simplificada que calcula automaticamente datas e legendas baseado nos dados fornecidos.

## 🚀 **Nova API Simplificada**

O componente foi refatorado para uma experiência de desenvolvedor muito mais simples, consolidando múltiplas props em uma única estrutura de dados.

### **Antes (API Complexa)**
```vue
<farm-gantt-chart
  :groups="groups"
  :startDate="startDate"
  :endDate="endDate"
  :legendItems="legendItems"
  :barTypes="barTypes"
  @bar-click="handleBarClick"
/>
```

### **Depois (API Simplificada)**
```vue
<farm-gantt-chart 
  :data="ganttData" 
  @bar-click="handleBarClick" 
/>
```

## 📋 **Estrutura de Dados**

```typescript
interface GanttData {
  groups: GanttGroup[];
}

interface GanttGroup {
  title: string; // obrigatório
  bars: GanttBar[];
}

interface GanttBar {
  id: string | number;
  label: string; // obrigatório
  start: Date | string; // obrigatório
  end: Date | string; // obrigatório
  color: string; // obrigatório - cor direta hex/rgb
  [key: string]: any; // propriedades extras
}
```

## ✨ **Funcionalidades Automáticas**

### **1. Cálculo Automático de Datas**
- Calcula automaticamente do primeiro dia do menor mês até o último dia do maior mês de todas as barras
- **Sem necessidade** de fornecer `startDate` e `endDate` manualmente
- **Sem margem** - usa exatamente o range dos dados

### **2. Geração Automática de Legenda**
- Gera automaticamente baseada em cores e labels únicos encontrados nas barras
- **Sem necessidade** de criar `legendItems` manualmente
- **Elimina redundância** entre dados e legenda

### **3. Cores Diretas**
- Cores definidas diretamente nas barras com propriedade `color`
- **Elimina necessidade** do prop `barTypes`
- **Suporte completo** a cores hex, rgb, hsl, etc.

## 📝 **Exemplo de Uso**

```vue
<template>
  <farm-gantt-chart 
    :data="ganttData" 
    @bar-click="handleBarClick" 
  />
</template>

<script>
import { GanttChart } from '@/components/GanttChart';

export default {
  components: {
    'farm-gantt-chart': GanttChart
  },
  data() {
    return {
      ganttData: {
        groups: [
          {
            title: 'Campanha 2025',
            bars: [
              {
                id: 1,
                label: 'Vigência da Campanha',
                start: new Date(2025, 0, 1),
                end: new Date(2025, 5, 15),
                color: '#7BC4F7'
              },
              {
                id: 2,
                label: 'Período de Desembolso',
                start: new Date(2025, 2, 1),
                end: new Date(2025, 4, 30),
                color: '#FFB84D'
              }
            ]
          }
        ]
      }
    };
  },
  methods: {
    handleBarClick(bar) {
      console.log('Bar clicked:', bar);
    }
  }
};
</script>
```

## 📦 **Exportações Disponíveis**

```typescript
// Componente
import GanttChart from '@/components/GanttChart';

// Tipos TypeScript
import type { 
  GanttData, 
  GanttGroup, 
  GanttBar, 
  LegendItem 
} from '@/components/GanttChart';

// Utilitários de Data
import { 
  getMonthsBetween,
  formatMonth,
  isCurrentMonth,
  getDaysInMonth,
  getColumnForDate
} from '@/components/GanttChart';
```

## 🎯 **Benefícios**

- **90% menos código** para usar o componente
- **Zero configuração manual** de datas e legenda
- **API mais intuitiva** - apenas dados essenciais
- **Funcionalidade visual idêntica** mantida
- **Melhor performance** - menos watchers do Vue
- **Menos bugs** - eliminação de inconsistências entre props
- **TypeScript nativo** - tipos bem definidos

## 📁 **Estrutura de Arquivos**

```
src/components/GanttChart/
├── GanttChart.vue          # Componente principal
├── GanttChart.scss         # Estilos
├── GanttChart.stories.js   # Stories do Storybook
├── index.ts               # Exportações principais
├── README.md              # Esta documentação
├── types/
│   └── index.ts           # Interfaces TypeScript
├── utils/
│   └── dateUtils.ts       # Utilitários de data
└── __tests__/
    └── GanttChart.spec.js # Testes unitários
```

## 🔄 **Migração**

Para migrar do formato antigo, simplesmente:

1. **Consolidar dados** em um objeto `data`
2. **Mover cores** de `barTypes` para propriedade `color` das barras
3. **Remover** props `startDate`, `endDate`, `legendItems`, `barTypes`
4. **Renomear** `group.label` para `group.title`

Todas as funcionalidades visuais e de eventos permanecem idênticas.