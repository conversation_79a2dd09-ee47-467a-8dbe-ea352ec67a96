@import '../../../configurations/variables';
@import '../../../configurations/mixins';
@import '../../../configurations/theme-colors';
$butonSizes: (
	'xs': 20px,
	'sm': 28px,
	'md': 32px,
	'lg': 48px,
	'xl': 56px,
	'default': 36px,
);

.farm-btn {
	font-size: 14px;
	align-items: center;
	border-radius: 6px;
	display: inline-flex;
	flex: 0 0 auto;
	font-weight: 700;
	letter-spacing: 0.5px;
	justify-content: center;
	outline: 0;
	position: relative;
	text-decoration: none;
	transition-duration: 0.28s;
	transition-property: box-shadow, transform, opacity;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	user-select: none;
	vertical-align: middle;
	white-space: nowrap;
	line-height: 16px;
	height: 36px;
	min-width: 64px;
	padding: 0 16px;

	&.farm-btn--full {
		width: 100%;
	}

	@each $size,
	$value in $sizes {
		&#{'.farm-btn[size=' + $size + ']'} {
			font-size: $value;
			height: map-get($butonSizes, $size);
		}
	}

	&__content {
		font-weight: 700;
		display: flex;
		flex-direction: row;
		align-items: center;
		gap: 8px;

		::v-deep i.mdi {
			font-size: 1rem;
		}
	}

	&--round {
		border-radius: 50%;
	}

	&.farm-btn--disabled {
		background-color: #dadada;
		color: rgba(0, 0, 0, 0.26);
		cursor: default;
		border-color: #dadada;

		&:before {
			display: none;
		}

		i.mdi.farm-icon {
			color: rgba(0, 0, 0, 0.26);
		}
	}

	&:before {
		transition: all 0.28s ease;
		background-color: currentColor;
		border-radius: inherit;
		bottom: 0;
		color: inherit;
		content: '';
		left: 0;
		opacity: 0;
		pointer-events: none;
		position: absolute;
		right: 0;
		top: 0;
	}

	&:not(.farm-btn--plain):hover {
		&:before {
			opacity: 0.14;
		}
	}

	&:not(.farm-btn--plain):not(.farm-btn--disabled):active {
		&:before {
			opacity: 0.27;
		}
	}

	&.farm-btn--plain {
		background-color: transparent !important;

		.farm-btn__content {
			position: relative;

			&:before {
				content: '';
				transition: all 0.28s ease;
				background-color: currentColor;
				top: 110%;
				left: 0;
				position: absolute;
				width: 100%;
				height: 1px;
				opacity: 0;
			}
		}

		&:hover:not(.farm-btn--disabled) {
			.farm-btn__content,
			.farm-btn__content:before {
				opacity: 0.86;
			}
		}

		&:active {
			&:before {
				opacity: 0.73;
			}
		}
	}

	&.farm-btn--rounded {
		border-radius: 1rem;
	}
}

.farm-btn.farm-btn--icon {
	background: transparent !important;
	border: transparent !important;
	transition: color 0.5s ease;

	min-width: auto;
	width: 36px;

	.farm-btn__content i.mdi {
		color: gray;
	}

	@each $size,
	$value in $sizes {
		&#{'.farm-btn[size=' + $size + ']'} {
			font-size: $value;
		}
	}

	&[size='lg'] {
		width: 48px;
		height: 48px;
	}

	&[size='xl'] {
		width: 64px;
		height: 64px;
	}

	@each $color in $theme-colors-list {
		&#{'.farm-btn--' + $color}:not(.farm-btn--disabled) {
			&:hover {
				color: var(--farm-#{$color}-base);
			}

			.farm-btn__content :deep(i.mdi) {
				color: var(--farm-#{$color}-base);
			}

			&.farm-btn--variation-lighten {
				&:hover {
					color: var(--farm-#{$color}-lighten);
				}

				.farm-btn__content :deep(i.mdi) {
					color: var(--farm-#{$color}-lighten);
				}
			}

			&.farm-btn--variation-darken {
				&:hover {
					color: var(--farm-#{$color}-darken);
				}

				.farm-btn__content :deep(i.mdi) {
					color: var(--farm-#{$color}-darken);
				}
			}
		}
	}

	&#{'.farm-btn--white'}:not(.farm-btn--disabled) {
		::v-deep .farm-btn__content i.mdi {
			color: white;
		}
	}
}

@each $color in $theme-colors-list {
	#{'.farm-btn--' + $color} {
		background-color: var(--farm-#{$color}-base);
		color: white;

		&.farm-btn--outlined {
			border: 1px solid var(--farm-#{$color}-base);
		}

		&:not(.farm-btn--outlined):not(.farm-btn--plain):not(.farm-btn--icon) {
			::v-deep .farm-btn__content {
				i.mdi {
					color: white;
				}

				.farm-btn__content i.mdi {
					color: black;
				}
			}
		}

		&.farm-btn--variation-lighten {
			border: 1px solid var(--farm-#{$color}-lighten);
			background-color: var(--farm-#{$color}-lighten);
			color: var(--farm-text-primary);
		}

		&.farm-btn--variation-darken {
			border: 1px solid var(--farm-#{$color}-darken);
			background-color: var(--farm-#{$color}-darken);
		}
	}

	.farm-btn--extra,
	.farm-btn--white {
		color: black;
	}

	:not(.farm-btn--disabled) {
		&.farm-btn--outlined#{'.farm-btn--' + $color} {
			border: 1px solid var(--farm-#{$color}-base);
			background: white;
			color: var(--farm-#{$color}-base);

			&.farm-btn--extra,
			&.farm-btn--white {
				color: black;
				border: 1px solid black;
			}

			.farm-btn__content i.mdi {
				color: var(--farm-#{$color}-base);
			}
		}

		&.farm-btn--plain#{'.farm-btn--' + $color} {
			border: none;
			background: white;
			color: var(--farm-#{$color}-base);

			&.farm-btn--extra,
			&.farm-btn--white {
				color: black;

				.farm-btn__content i.mdi {
					color: black;
				}
			}

			.farm-btn__content i.mdi {
				color: var(--farm-#{$color}-base);
			}
		}
	}
}

.farm-btn--outlined.farm-btn--disabled {
	border: 1px solid #d6d6d6;
	color: #d6d6d6;
	background-color: white;

	i.mdi.farm-icon {
		color: #d6d6d6;
	}
}

.farm-btn--primary.farm-btn--disabled:not(.farm-btn--outlined) {
	.farm-btn__content i.mdi {
		color: rgba(0, 0, 0, 0.26) !important;
	}
}

.farm-btn--plain.farm-btn--disabled {
	background-color: transparent;
	border: none;
}

.farm-btn--elevated {
	box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%), 0px 2px 2px 0px rgb(0 0 0 / 14%),
		0px 1px 5px 0px rgb(0 0 0 / 12%);
}

@include upToSm {
	.farm-btn {
		padding: 0 12px;
		line-height: 12px;
		min-width: 48px;
		height: 32px;
	}
}

@include forXsOnly {
	.farm-btn.farm-btn--responsive {
		width: 100%;
		margin: 0;
	}
}