name: PR develop to main
on:
  workflow_dispatch:

jobs:
  update_version:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:

      - name: Checkout source code
        uses: actions/checkout@master

      - uses: actions/setup-node@v3
        with:
          node-version: 14.19
          registry-url: https://registry.npmjs.org/
        
      - name: Get version from package.json
        run: node -p -e '`PACKAGE_VERSION=${require("./package.json").version}`' >> $GITHUB_ENV

      - name: Current version package.json
        run: echo ${{ env.PACKAGE_VERSION }}
      
      - name: Create tag using value from package.json
        uses: pkgdeps/git-tag-action@v2
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          github_repo: ${{ github.repository }}
          version: ${{ env.PACKAGE_VERSION }}
          git_commit_sha: ${{ github.sha }}
          git_tag_prefix: "v"

      - name: Create PR from develop to main
        uses: repo-sync/pull-request@v2
        with:
          source_branch: "develop"
          destination_branch: "main"
          github_token: ${{ secrets.GITHUB_TOKEN }}
          pr_label: "automerge,triggered pr"
          pr_title: "Pulling current version from develop v${{ env.PACKAGE_VERSION }} to main"
