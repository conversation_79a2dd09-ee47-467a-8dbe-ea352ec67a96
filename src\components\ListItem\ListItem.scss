@import '../../configurations/theme-colors';

.farm-listitem {
	list-style-type: none;
	transition: background-color 300ms linear;
	padding: 8px;
	height: 36px;
	display: flex;
	align-items: center;

	.farm-icon,
	.farm-typography {
		color: var(--farm-neutral-darken);
	}

	&--clickable {
		cursor: pointer;
	}

	&--disabled {
		cursor: default;

		.farm-icon,
		.farm-typography {
			color: var(--farm-stroke-disabled);
		}
	}

	> a {
		text-decoration: none;
	}

	&:hover,
	&:focus {
		border-radius: 5px;

		&:not(.farm-listitem--disabled) {
			@each $color in $theme-colors-list {
				&#{'.farm-listitem--' + $color + '-base'} {
					background-color: rgba(themeColor($color), 0.27);
					.farm-icon,
					.farm-typography {
						color: rgba(themeColor($color), 1);
					}
				}

				&#{'.farm-listitem--' + $color + '-lighten'} {
					background-color: rgba(themeColor($color, 'lighten'), 0.27);
					.farm-icon,
					.farm-typography {
						color: rgba(themeColor($color), 1);
					}
				}

				&#{'.farm-listitem--' + $color + '-darken'} {
					background-color: rgba(themeColor($color, 'darken'), 0.27);
					.farm-icon,
					.farm-typography {
						color: rgba(themeColor($color), 1);
					}
				}
			}
		}
	}

	&:active {
		&:not(.farm-listitem--disabled) {
			@each $color in $theme-colors-list {
				&#{'.farm-listitem--' + $color + '-base'} {
					background-color: rgba(themeColor($color), 0.8);
				}

				&#{'.farm-listitem--' + $color + '-lighten'} {
					background-color: rgba(themeColor($color, 'lighten'), 0.8);
				}

				&#{'.farm-listitem--' + $color + '-darken'} {
					background-color: rgba(themeColor($color, 'darken'), 0.8);
				}
			}
		}
	}
}
